//! Ultra‑low‑latency stack‑allocated Binance order book implementation.
//!
//! * Completely allocation‑free after start‑up: no Vec, HashMap or heap at runtime.
//! * Suitable for `#![no_std]` environments (you can disable the few debug assertions).
//! * O(log N) search, O(N) insert/delete with `ptr::copy` memcpy‑style shifts.
//! * Best for depths up to a few thousand levels (N <= 5000) which covers Binance full book.
//!
//! Adapted from Binance “How to manage a local order book correctly” steps — see developers.binance.com.
//!
//! Compile with: `cargo build --release` (requires Rust 1.77+ for const‑generics).

use core::cmp::Ordering;
use core::ptr;
use std::cmp::max;
use std::fmt::Display;

/// A single order book level (price, quantity).
#[derive(Clone, Copy, Debug, Default)]
#[repr(C)]
pub struct Level {
    pub price: f64,
    pub qty: f64,
}

/// A stack‑allocated side (bid or ask).
///
/// * `N` : maximum number of levels to keep.
/// * `IS_BID` : `true` for bids (sorted DESC), `false` for asks (ASC).
#[derive(Clone, Copy, Debug)]
pub struct Side<const N: usize, const IS_BID: bool> {
    levels: [Level; N],
    len: usize,
}

impl<const N: usize, const IS_BID: bool> Side<N, IS_BID> {
    /// Empty side.
    #[inline]
    pub const fn new() -> Self {
        Self {
            levels: [Level {
                price: 0.0,
                qty: 0.0,
            }; N],
            len: 0,
        }
    }

    /// Binary search for a price. Returns (found, index).
    #[inline(always)]
    fn find(&self, price: f64) -> (bool, usize) {
        let mut low = 0usize;
        let mut high = self.len;
        while low < high {
            let mid = (low + high) >> 1;
            let mid_price = unsafe { self.levels.get_unchecked(mid).price };
            let ord = if IS_BID {
                if price > mid_price {
                    Ordering::Less
                } else if price < mid_price {
                    Ordering::Greater
                } else {
                    Ordering::Equal
                }
            } else {
                if price < mid_price {
                    Ordering::Less
                } else if price > mid_price {
                    Ordering::Greater
                } else {
                    Ordering::Equal
                }
            };

            match ord {
                Ordering::Less => high = mid,
                Ordering::Greater => low = mid + 1,
                Ordering::Equal => return (true, mid),
            }
        }
        (false, low)
    }

    /// Insert/update level. `qty == 0` removes.
    #[inline]
    pub fn upsert(&mut self, price: f64, qty: f64) {
        let (found, idx) = self.find(price);

        if found {
            if qty == 0.0 {
                // delete
                unsafe {
                    let dst = self.levels.as_mut_ptr().add(idx);
                    let src = dst.add(1);
                    ptr::copy(src, dst, self.len - idx - 1);
                }
                self.len -= 1;
            } else {
                unsafe {
                    self.levels.get_unchecked_mut(idx).qty = qty;
                }
            }
        } else if qty != 0.0 {
            // Check capacity to prevent array bounds overflow
            if self.len >= N {
                // For a 20-depth orderbook, discard levels beyond capacity
                // In a real trading system, you might want to:
                // 1. Replace the worst level if this is a better price
                // 2. Log a warning about dropped levels
                // 3. Implement a more sophisticated capacity management strategy
                return;
            }

            unsafe {
                let dst = self.levels.as_mut_ptr().add(idx + 1);
                let src = self.levels.as_ptr().add(idx);
                ptr::copy(src, dst, self.len - idx); // shift right
                let level = self.levels.get_unchecked_mut(idx);
                level.price = price;
                level.qty = qty;
            }
            self.len += 1;
        }
    }

    #[inline]
    pub fn iter(&self) -> impl Iterator<Item = &Level> {
        self.levels[..self.len].iter()
    }
}

/// Complete order book (bids + asks) with monotonic update sequence tracking.
#[derive(Clone, Copy)]
pub struct OrderBook<const N: usize> {
    pub last_update_id: u64,
    bids: Side<N, true>,
    asks: Side<N, false>,
}

impl<const N: usize> OrderBook<N> {
    /// Empty book.
    pub const fn new() -> Self {
        Self {
            last_update_id: 0,
            bids: Side::new(),
            asks: Side::new(),
        }
    }

    fn check_overlap(&self) {
        if self.bids.len > 0 && self.asks.len > 0 {
            if self.bids.levels[0].price >= self.asks.levels[0].price {
                panic!("Bids and asks overlap: {}", self);
            }
        }
    }

    /// Apply REST snapshot.
    pub fn apply_snapshot(
        &mut self,
        last_update_id: u64,
        bids: &[(f64, f64)],
        asks: &[(f64, f64)],
    ) -> bool {
        if last_update_id <= self.last_update_id {
            return false;
        }
        self.last_update_id = last_update_id;
        self.bids.len = 0;
        self.asks.len = 0;

        for &(p, q) in bids.iter().take(N) {
            if p == 0.0 {
                break;
            }
            self.bids.upsert(p, q);
        }
        for &(p, q) in asks.iter().take(N) {
            if p == 0.0 {
                break;
            }
            self.asks.upsert(p, q);
        }
        self.check_overlap();
        true
    }

    pub fn apply_diff(
        &mut self,
        last_update_id: u64,
        bid_updates: &[(f64, f64)],
        ask_updates: &[(f64, f64)],
    ) -> bool {
        if last_update_id <= self.last_update_id || self.last_update_id == 0 {
            return false;
        }
        for &(p, q) in bid_updates {
            if p == 0.0 {
                break;
            }
            self.bids.upsert(p, q);
        }
        for &(p, q) in ask_updates {
            if p == 0.0 {
                break;
            }
            self.asks.upsert(p, q);
        }
        self.last_update_id = last_update_id;
        self.check_overlap();
        true
    }

    #[inline]
    pub fn best_bid(&self) -> Option<&Level> {
        if self.bids.len > 0 {
            Some(&self.bids.levels[0])
        } else {
            None
        }
    }

    #[inline]
    pub fn best_ask(&self) -> Option<&Level> {
        if self.asks.len > 0 {
            Some(&self.asks.levels[0])
        } else {
            None
        }
    }

    #[inline]
    pub fn bid_count(&self) -> usize {
        self.bids.len
    }

    #[inline]
    pub fn ask_count(&self) -> usize {
        self.asks.len
    }
}

impl<const N: usize> Display for OrderBook<N> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Orderbook Snapshot: \n").unwrap();
        write!(f, "Last Update ID: {}\n", self.last_update_id).unwrap();
        let max_len = max(self.bids.len, self.asks.len);
        for i in 0..max_len {
            if i < self.bids.len {
                write!(
                    f,
                    "    Bid: {}@{}\t\t",
                    self.bids.levels[i].price, self.bids.levels[i].qty
                )
                .unwrap();
            } else {
                write!(f, "\t\t\t\t").unwrap();
            }
            if i < self.asks.len {
                write!(
                    f,
                    "    Ask: {}@{}\n",
                    self.asks.levels[i].price, self.asks.levels[i].qty
                )
                .unwrap();
            } else {
                write!(f, "\t\t\t\t").unwrap();
            }
        }
        write!(f, "\n")
    }
}

/// Example usage (depth 1000).
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn smoke() {
        const N: usize = 1000;
        let mut book: OrderBook<N> = OrderBook::new();
        // snapshot
        book.apply_snapshot(10, &[(100.0, 1.0)], &[(101.0, 1.2)]);
        assert_eq!(book.best_bid().unwrap().price, 100.0);

        // diff update
        book.apply_diff(11, &[(100.0, 2.0)], &[(101.0, 0.0)]);
        assert_eq!(book.best_bid().unwrap().qty, 2.0);
        assert!(book.best_ask().is_none());
    }

    #[test]
    fn test_20_depth_limit() {
        // Test with 20 depth limit as mentioned by user
        const N: usize = 20;
        let mut book: OrderBook<N> = OrderBook::new();

        // Create more than 20 bid levels to test capacity overflow
        let mut bids = Vec::new();
        let mut asks = Vec::new();

        // Generate 25 bid levels (more than capacity of 20)
        for i in 0..25 {
            bids.push((100.0 - i as f64, 1.0)); // Descending prices for bids
        }

        // Generate 25 ask levels (more than capacity of 20)
        for i in 0..25 {
            asks.push((101.0 + i as f64, 1.0)); // Ascending prices for asks
        }

        // This should not crash - only first 20 levels should be kept
        book.apply_snapshot(1, &bids, &asks);

        // Verify we only have 20 levels max
        assert_eq!(book.bids.len, 20);
        assert_eq!(book.asks.len, 20);

        // Verify best bid/ask are correct
        assert_eq!(book.best_bid().unwrap().price, 100.0);
        assert_eq!(book.best_ask().unwrap().price, 101.0);
    }

    #[test]
    fn test_capacity_overflow_with_diff_updates() {
        // Test capacity overflow when applying diff updates
        const N: usize = 20;
        let mut book: OrderBook<N> = OrderBook::new();

        // Start with a full book (20 levels each side)
        let mut bids = Vec::new();
        let mut asks = Vec::new();

        for i in 0..20 {
            bids.push((100.0 - i as f64, 1.0));
            asks.push((101.0 + i as f64, 1.0));
        }

        book.apply_snapshot(1, &bids, &asks);
        assert_eq!(book.bids.len, 20);
        assert_eq!(book.asks.len, 20);

        // Now try to add more levels via diff updates - this might cause overflow
        let new_bid_updates = vec![
            (120.0, 1.0), // New best bid
            (75.0, 1.0),  // New worst bid (should be rejected if > 20 levels)
        ];

        let new_ask_updates = vec![
            (80.0, 1.0),  // New best ask
            (130.0, 1.0), // New worst ask (should be rejected if > 20 levels)
        ];

        // This might cause coredump if capacity checking is not proper
        book.apply_diff(2, &new_bid_updates, &new_ask_updates);

        // Should still have at most 20 levels
        assert!(book.bids.len <= 20);
        assert!(book.asks.len <= 20);
    }

    #[test]
    fn test_edge_case_exact_capacity() {
        // Test exactly at capacity boundary
        const N: usize = 20;
        let mut book: OrderBook<N> = OrderBook::new();

        // Add exactly 20 levels
        let mut bids = Vec::new();
        for i in 0..20 {
            bids.push((100.0 - i as f64, 1.0));
        }

        book.apply_snapshot(1, &bids, &[]);
        assert_eq!(book.bids.len, 20);

        // Try to add one more level - this should either be rejected or replace worst level
        book.apply_diff(2, &[(70.0, 1.0)], &[]); // Price worse than existing levels

        // Should still have exactly 20 levels
        assert_eq!(book.bids.len, 20);
    }

    #[test]
    fn test_remove_levels_with_zero_qty() {
        const N: usize = 20;
        let mut book: OrderBook<N> = OrderBook::new();

        // Add some levels
        let bids = vec![(100.0, 1.0), (99.0, 2.0), (98.0, 3.0)];
        book.apply_snapshot(1, &bids, &[]);
        assert_eq!(book.bids.len, 3);

        // Remove middle level with qty = 0
        book.apply_diff(2, &[(99.0, 0.0)], &[]);
        assert_eq!(book.bids.len, 2);

        // Verify correct levels remain
        assert_eq!(book.best_bid().unwrap().price, 100.0);
        let mut iter = book.bids.iter();
        iter.next(); // Skip first
        assert_eq!(iter.next().unwrap().price, 98.0);
    }
}
