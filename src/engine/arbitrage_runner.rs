use std::{
    net::{IpAddr, Ipv4Addr, SocketAddr},
    str::FromStr,
};

use mio::Token;

use crate::{
    CallbackData, EdgeDirection, Message, RING_FILLED_ORDERS, Result, Settings, TRADING_PAIR_RATES,
    TradingPair, WebSocket, WebSocketHandle,
    encoding::sbe::{
        parse_sbe_bookticker, parse_sbe_depth_snapshot, parse_sbe_depth_update,
        trade::parse_sbe_trades,
    },
    engine::{
        self,
        arbitrage_engine::ArbitrageEngine,
        binance::{
            generate_depth_20_ws_url, generate_depth_diff_ws_url, generate_market_data_ws_url,
            generate_order_url, generate_trade_ws_url,
        },
        monitor::{CURRENT_RING_INDEX, CURRENT_RING_TS, init_latency_stats},
        trade::{
            generate_order_request_by_symbol, generate_order_requests,
            generate_session_logon_request, generate_trading_fee_request,
            generate_user_data_sub_request, update_trading_fee,
        },
        trading_pair::{API_KEY, TRADING_PAIR_COUNT},
    },
    flush_logs, logln,
    net::utils::url::Url,
    utils::{self, perf::circles_to_ns},
};
const REST_TOKEN: Token = Token(0);

const MARKET_DATA_TOKEN_1: Token = Token(10);
const MARKET_DATA_TOKEN_2: Token = Token(11);
const MARKET_DATA_TOKEN_3: Token = Token(12);
const MARKET_DATA_TOKEN_4: Token = Token(13);
const MARKET_DATA_TOKEN_5: Token = Token(14);
const MARKET_DATA_TOKEN_6: Token = Token(15);
const MARKET_DATA_TOKEN_7: Token = Token(16);
const MARKET_DATA_TOKEN_8: Token = Token(17);
const MARKET_DATA_TOKEN_9: Token = Token(18);
const MARKET_DATA_TOKEN_10: Token = Token(19);

const ORDER_TOKEN_1: Token = Token(21);
const ORDER_TOKEN_2: Token = Token(22);
const ORDER_TOKEN_3: Token = Token(23);
const ORDER_TOKEN_4: Token = Token(24);

const TRADE_TOKEN_1: Token = Token(31);
const TRADE_TOKEN_2: Token = Token(32);
const TRADE_TOKEN_3: Token = Token(33);
const TRADE_TOKEN_4: Token = Token(34);

const DEPTH_DIFF_TOKEN_1: Token = Token(41);
const DEPTH_DIFF_TOKEN_2: Token = Token(42);
const DEPTH_DIFF_TOKEN_3: Token = Token(43);
const DEPTH_DIFF_TOKEN_4: Token = Token(44);

const DEPTH_SNAPSHOT_TOKEN_1: Token = Token(51);
const DEPTH_SNAPSHOT_TOKEN_2: Token = Token(52);
const DEPTH_SNAPSHOT_TOKEN_3: Token = Token(53);
const DEPTH_SNAPSHOT_TOKEN_4: Token = Token(54);

const REST_HOST: &str = "https://api.binance.com";

pub fn run(
    market_ip: Option<String>,
    trade_ip: Option<String>,
    order_ip: Option<String>,
) -> Result<()> {
    init_latency_stats();

    let mut last_test_order_time: u64 = utils::perf::now();
    let mut trigger_pair = TradingPair::from(0);

    const N: usize = 1024 * 32;
    let callback = move |handle: &mut WebSocketHandle<N>, cd: CallbackData| -> Result<()> {
        let now = utils::perf::now();
        match cd {
            CallbackData::Message(token, msg) => match msg {
                Message::WebsocketPayload(data) => match token {
                    MARKET_DATA_TOKEN_1 | MARKET_DATA_TOKEN_2 | MARKET_DATA_TOKEN_3
                    | MARKET_DATA_TOKEN_4 | MARKET_DATA_TOKEN_5 | MARKET_DATA_TOKEN_6
                    | MARKET_DATA_TOKEN_7 | MARKET_DATA_TOKEN_8 | MARKET_DATA_TOKEN_9
                    | MARKET_DATA_TOKEN_10 => {
                        if let Some(bt) = parse_sbe_bookticker(data.as_ref()) {
                            if !ArbitrageEngine::update_rate(&bt) {
                                return Ok(());
                            }
                            if !ArbitrageEngine::check_arbitrage_conditions(now) {
                                return Ok(());
                            }
                            if let Some(ring_index) = ArbitrageEngine::check_arbitrage(bt.symbol) {
                                if ArbitrageEngine::compute_orders(ring_index).is_some() {
                                    trigger_pair = TradingPair::from(bt.symbol);
                                    let buf = handle.get_write_buf(ORDER_TOKEN_1)?;
                                    generate_order_requests(ring_index, now, buf, 0);
                                    handle.trigger_write(ORDER_TOKEN_1)?;
                                    let buf = handle.get_write_buf(ORDER_TOKEN_2)?;
                                    generate_order_requests(ring_index, now, buf, 1);
                                    handle.trigger_write(ORDER_TOKEN_2)?;
                                    let buf = handle.get_write_buf(ORDER_TOKEN_3)?;
                                    generate_order_requests(ring_index, now, buf, 2);
                                    handle.trigger_write(ORDER_TOKEN_3)?;
                                    if unsafe { RING_FILLED_ORDERS[ring_index][0] } == 4 {
                                        let buf = handle.get_write_buf(ORDER_TOKEN_4)?;
                                        generate_order_requests(ring_index, now, buf, 3);
                                        handle.trigger_write(ORDER_TOKEN_4)?;
                                    }
                                } else {
                                    logln!("no orders");
                                }
                                unsafe {
                                    CURRENT_RING_INDEX = ring_index;
                                    CURRENT_RING_TS = now;
                                }
                            } else if circles_to_ns(now - last_test_order_time) > 1_000_000_000.0 {
                                // 定期发送测试订单以保持连接活跃
                                let pair =
                                    TradingPair::from(now as usize % TRADING_PAIR_COUNT as usize);
                                let price = unsafe {
                                    TRADING_PAIR_RATES[pair as usize]
                                        [EdgeDirection::Reverse as usize]
                                };
                                if price <= 0.0 {
                                    return Ok(());
                                }
                                let buf = handle.get_write_buf(ORDER_TOKEN_1)?;
                                let pair_str = pair.to_str();
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                handle.trigger_write(ORDER_TOKEN_1)?;
                                let buf = handle.get_write_buf(ORDER_TOKEN_2)?;
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                handle.trigger_write(ORDER_TOKEN_2)?;
                                let buf = handle.get_write_buf(ORDER_TOKEN_3)?;
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                handle.trigger_write(ORDER_TOKEN_3)?;
                                let buf = handle.get_write_buf(ORDER_TOKEN_4)?;
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                handle.trigger_write(ORDER_TOKEN_4)?;
                                last_test_order_time = now;
                            }
                        } else {
                            logln!("failed to parse market data");
                        }
                    }
                    DEPTH_DIFF_TOKEN_1 | DEPTH_DIFF_TOKEN_2 | DEPTH_DIFF_TOKEN_3
                    | DEPTH_DIFF_TOKEN_4 => {
                        if let Some(depth_diff) = parse_sbe_depth_update(data.as_ref()) {
                            // updates are sorted by exchange already
                            if !ArbitrageEngine::update_orderbook_by_updates(&depth_diff) {
                                return Ok(());
                            }
                            if !ArbitrageEngine::check_arbitrage_conditions(now) {
                                return Ok(());
                            }
                            if let Some(ring_index) =
                                ArbitrageEngine::check_arbitrage(depth_diff.symbol)
                            {
                                if ArbitrageEngine::compute_orders(ring_index).is_some() {
                                    trigger_pair = TradingPair::from(depth_diff.symbol);
                                    let buf = handle.get_write_buf(ORDER_TOKEN_1)?;
                                    generate_order_requests(ring_index, now, buf, 0);
                                    handle.trigger_write(ORDER_TOKEN_1)?;
                                    let buf = handle.get_write_buf(ORDER_TOKEN_2)?;
                                    generate_order_requests(ring_index, now, buf, 1);
                                    handle.trigger_write(ORDER_TOKEN_2)?;
                                    let buf = handle.get_write_buf(ORDER_TOKEN_3)?;
                                    generate_order_requests(ring_index, now, buf, 2);
                                    handle.trigger_write(ORDER_TOKEN_3)?;
                                    if unsafe { RING_FILLED_ORDERS[ring_index][0] } == 4 {
                                        let buf = handle.get_write_buf(ORDER_TOKEN_4)?;
                                        generate_order_requests(ring_index, now, buf, 3);
                                        handle.trigger_write(ORDER_TOKEN_4)?;
                                    }
                                } else {
                                    logln!("no orders");
                                }
                                unsafe {
                                    CURRENT_RING_INDEX = ring_index;
                                    CURRENT_RING_TS = now;
                                }
                            }
                        } else {
                            logln!("failed to parse depth diff");
                        }
                    }
                    DEPTH_SNAPSHOT_TOKEN_1
                    | DEPTH_SNAPSHOT_TOKEN_2
                    | DEPTH_SNAPSHOT_TOKEN_3
                    | DEPTH_SNAPSHOT_TOKEN_4 => {
                        if let Some(depth_snapshot) = parse_sbe_depth_snapshot(data.as_ref()) {
                            if !ArbitrageEngine::update_orderbook_by_snapshot(&depth_snapshot) {
                                return Ok(());
                            }
                            if !ArbitrageEngine::check_arbitrage_conditions(now) {
                                return Ok(());
                            }
                            if let Some(ring_index) =
                                ArbitrageEngine::check_arbitrage(depth_snapshot.symbol)
                            {
                                if ArbitrageEngine::compute_orders(ring_index).is_some() {
                                    trigger_pair = TradingPair::from(depth_snapshot.symbol);
                                    let buf = handle.get_write_buf(ORDER_TOKEN_1)?;
                                    generate_order_requests(ring_index, now, buf, 0);
                                    handle.trigger_write(ORDER_TOKEN_1)?;
                                    let buf = handle.get_write_buf(ORDER_TOKEN_2)?;
                                    generate_order_requests(ring_index, now, buf, 1);
                                    handle.trigger_write(ORDER_TOKEN_2)?;
                                    let buf = handle.get_write_buf(ORDER_TOKEN_3)?;
                                    generate_order_requests(ring_index, now, buf, 2);
                                    handle.trigger_write(ORDER_TOKEN_3)?;
                                    if unsafe { RING_FILLED_ORDERS[ring_index][0] } == 4 {
                                        let buf = handle.get_write_buf(ORDER_TOKEN_4)?;
                                        generate_order_requests(ring_index, now, buf, 3);
                                        handle.trigger_write(ORDER_TOKEN_4)?;
                                    }
                                } else {
                                    logln!("no orders");
                                }
                                unsafe {
                                    CURRENT_RING_INDEX = ring_index;
                                    CURRENT_RING_TS = now;
                                }
                            }
                        } else {
                            logln!("failed to parse depth snapshot");
                        }
                    }
                    TRADE_TOKEN_1 | TRADE_TOKEN_2 | TRADE_TOKEN_3 | TRADE_TOKEN_4 => {
                        if let Some(trade) = parse_sbe_trades(data.as_ref()) {
                            ArbitrageEngine::update_rate_by_trades(trade);
                        } else {
                            logln!("failed to parse trade");
                        }
                    }
                    ORDER_TOKEN_1 | ORDER_TOKEN_2 | ORDER_TOKEN_3 | ORDER_TOKEN_4 => {
                        if !ArbitrageEngine::user_data_subscribed() {
                            handle
                                .send_message(token, generate_user_data_sub_request())
                                .unwrap();
                            ArbitrageEngine::set_user_data_subscribed();
                        }
                        engine::monitor::monitor_order_execution(
                            token.0 - 1,
                            data.as_ref(),
                            trigger_pair,
                        );
                    }
                    _ => (),
                },
                Message::HttpResponse(response) => {
                    if let Some(body) = response.body.as_ref() {
                        update_trading_fee(body);
                        ArbitrageEngine::set_trading_fee_updated();
                        flush_logs!();
                    }
                }
                _ => (),
            },
            CallbackData::ConnectionOpen(token) => match token {
                ORDER_TOKEN_1 | ORDER_TOKEN_2 | ORDER_TOKEN_3 | ORDER_TOKEN_4 => {
                    logln!("order connection opened: {:?}", token);
                    handle.send_message(token, generate_session_logon_request())?;
                }
                REST_TOKEN => {
                    logln!("quering trading fee");
                    handle.send_message(REST_TOKEN, generate_trading_fee_request())?;
                }
                _ => (),
            },
            CallbackData::ConnectionClose(token) => {
                logln!("connection close: {:?}", token);
                flush_logs!();
            }
            CallbackData::ConnectionError(token, error) => {
                logln!("connection err: {:?}: {:?}", token, error);
                flush_logs!();
            }
        }
        Ok(())
    };
    let mut settings = Settings::default();
    settings.event_loop_timeout = Some(std::time::Duration::from_millis(1));
    let mut websocket = WebSocket::new(settings, callback)?;

    let mut headers = std::collections::HashMap::new();
    headers.insert("X-MBX-APIKEY".to_string(), API_KEY.to_string());

    let mut md_url: Url = generate_market_data_ws_url().into();
    if let Some(mip) = market_ip {
        md_url.socket_addr = Some(SocketAddr::new(
            IpAddr::V4(Ipv4Addr::from_str(&mip).unwrap()),
            md_url.port,
        ));
    }
    websocket.connect_with_headers(md_url.clone(), MARKET_DATA_TOKEN_1, headers.clone())?;
    websocket.connect_with_headers(md_url.clone(), MARKET_DATA_TOKEN_2, headers.clone())?;
    websocket.connect_with_headers(md_url.clone(), MARKET_DATA_TOKEN_3, headers.clone())?;
    websocket.connect_with_headers(md_url.clone(), MARKET_DATA_TOKEN_4, headers.clone())?;
    websocket.connect_with_headers(md_url.clone(), MARKET_DATA_TOKEN_5, headers.clone())?;
    websocket.connect_with_headers(md_url.clone(), MARKET_DATA_TOKEN_6, headers.clone())?;
    websocket.connect_with_headers(md_url.clone(), MARKET_DATA_TOKEN_7, headers.clone())?;
    websocket.connect_with_headers(md_url.clone(), MARKET_DATA_TOKEN_8, headers.clone())?;
    websocket.connect_with_headers(md_url.clone(), MARKET_DATA_TOKEN_9, headers.clone())?;
    websocket.connect_with_headers(md_url.clone(), MARKET_DATA_TOKEN_10, headers.clone())?;

    let mut trade_url: Url = generate_trade_ws_url().into();
    if let Some(trade_ip) = trade_ip {
        trade_url.socket_addr = Some(SocketAddr::new(
            IpAddr::V4(Ipv4Addr::from_str(&trade_ip).unwrap()),
            trade_url.port,
        ));
    }
    websocket.connect_with_headers(trade_url.clone(), TRADE_TOKEN_1, headers.clone())?;
    websocket.connect_with_headers(trade_url.clone(), TRADE_TOKEN_2, headers.clone())?;
    websocket.connect_with_headers(trade_url.clone(), TRADE_TOKEN_3, headers.clone())?;
    websocket.connect_with_headers(trade_url.clone(), TRADE_TOKEN_4, headers.clone())?;

    let mut order_url: Url = generate_order_url().into();
    if let Some(oip) = order_ip {
        order_url.socket_addr = Some(SocketAddr::new(
            IpAddr::V4(Ipv4Addr::from_str(&oip).unwrap()),
            order_url.port,
        ));
    }
    websocket.connect(order_url.clone(), ORDER_TOKEN_1)?;
    websocket.connect(order_url.clone(), ORDER_TOKEN_2)?;
    websocket.connect(order_url.clone(), ORDER_TOKEN_3)?;
    websocket.connect(order_url.clone(), ORDER_TOKEN_4)?;
    websocket.connect(REST_HOST, REST_TOKEN)?;

    let depth_diff_url: Url = generate_depth_diff_ws_url().into();
    websocket.connect_with_headers(depth_diff_url.clone(), DEPTH_DIFF_TOKEN_1, headers.clone())?;
    websocket.connect_with_headers(depth_diff_url.clone(), DEPTH_DIFF_TOKEN_2, headers.clone())?;
    websocket.connect_with_headers(depth_diff_url.clone(), DEPTH_DIFF_TOKEN_3, headers.clone())?;
    websocket.connect_with_headers(depth_diff_url.clone(), DEPTH_DIFF_TOKEN_4, headers.clone())?;

    let depth_snapshot_url: Url = generate_depth_20_ws_url().into();
    websocket.connect_with_headers(
        depth_snapshot_url.clone(),
        DEPTH_SNAPSHOT_TOKEN_1,
        headers.clone(),
    )?;
    websocket.connect_with_headers(
        depth_snapshot_url.clone(),
        DEPTH_SNAPSHOT_TOKEN_2,
        headers.clone(),
    )?;
    websocket.connect_with_headers(
        depth_snapshot_url.clone(),
        DEPTH_SNAPSHOT_TOKEN_3,
        headers.clone(),
    )?;
    websocket.connect_with_headers(
        depth_snapshot_url.clone(),
        DEPTH_SNAPSHOT_TOKEN_4,
        headers.clone(),
    )?;

    match websocket.run() {
        Ok(_) => (),
        Err(e) => {
            logln!("websocket run error: {:?}", e);
        }
    }
    Ok(())
}
