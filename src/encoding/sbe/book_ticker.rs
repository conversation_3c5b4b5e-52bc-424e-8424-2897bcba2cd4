// 移除未使用的导入，因为我们现在直接实现解析逻辑

/// SBE格式的BookTicker结构（基于Binance官方schema）
#[derive(Debug)]
pub struct SbeBookTicker<'a> {
    pub symbol: &'a str,
    pub bid_price: f64,
    pub bid_qty: f64,
    pub ask_price: f64,
    pub ask_qty: f64,
    pub event_time: u64,
    pub book_update_id: u64,
}

/// Binance SBE BestBidAskStreamEvent 字段偏移量（基于实际数据分析）
/// Template ID: 10001, 实际消息体长度: 58字节（而非schema中的50字节）
mod binance_offsets {
    // 固定字段部分（按照SBE schema顺序）
    pub const EVENT_TIME: usize = 0; // 8字节 - utcTimestampUs (int64)
    pub const BOOK_UPDATE_ID: usize = 8; // 8字节 - updateId (int64)
    pub const PRICE_EXPONENT: usize = 16; // 1字节 - exponent8 (int8)
    pub const QTY_EXPONENT: usize = 17; // 1字节 - exponent8 (int8)
    pub const BID_PRICE: usize = 18; // 8字节 - mantissa64 (int64)
    pub const BID_QTY: usize = 26; // 8字节 - mantissa64 (int64)
    pub const ASK_PRICE: usize = 34; // 8字节 - mantissa64 (int64)
    pub const ASK_QTY: usize = 42; // 8字节 - mantissa64 (int64)
    // 50-57字节：可能是额外字段或填充

    // 变长字段部分（在固定字段之后）
    pub const FIXED_FIELDS_SIZE: usize = 50; // schema中声明的block_length
    pub const SYMBOL_LENGTH_OFFSET: usize = 50; // 1字节 - varString8 length
    pub const SYMBOL_DATA_OFFSET: usize = 51; // 变长 - symbol data
}

/// 将mantissa和exponent转换为f64
fn mantissa_exponent_to_f64(mantissa: i64, exponent: i8) -> f64 {
    if mantissa == 0 {
        return 0.0;
    }

    let base = mantissa as f64;
    let power = 10.0_f64.powi(exponent as i32);
    base * power
}

/// 解析SBE格式的BookTicker消息（基于Binance官方schema）
///
/// Binance SBE BestBidAskStreamEvent消息格式：
/// - eventTime: 8字节 utcTimestampUs (int64)
/// - bookUpdateId: 8字节 updateId (int64)
/// - priceExponent: 1字节 exponent8 (int8)
/// - qtyExponent: 1字节 exponent8 (int8)
/// - bidPrice: 8字节 mantissa64 (int64)
/// - bidQty: 8字节 mantissa64 (int64)
/// - askPrice: 8字节 mantissa64 (int64)
/// - askQty: 8字节 mantissa64 (int64)
/// - symbol: 变长字符串 varString8 (1字节长度 + 数据)
///
/// # Arguments
/// * `data` - SBE消息体数据（不包含头部）
///
/// # Returns
/// * `Some(SbeBookTicker)` - 解析成功
/// * `None` - 解析失败
pub fn parse_sbe_bookticker(data: &[u8]) -> Option<SbeBookTicker<'_>> {
    use binance_offsets::*;
    let data = &data[8..];

    // 检查数据长度是否足够包含固定字段
    if data.len() < FIXED_FIELDS_SIZE {
        return None;
    }

    // 解析固定字段
    let event_time = i64::from_le_bytes([
        data[EVENT_TIME],
        data[EVENT_TIME + 1],
        data[EVENT_TIME + 2],
        data[EVENT_TIME + 3],
        data[EVENT_TIME + 4],
        data[EVENT_TIME + 5],
        data[EVENT_TIME + 6],
        data[EVENT_TIME + 7],
    ]) as u64;

    let book_update_id = i64::from_le_bytes([
        data[BOOK_UPDATE_ID],
        data[BOOK_UPDATE_ID + 1],
        data[BOOK_UPDATE_ID + 2],
        data[BOOK_UPDATE_ID + 3],
        data[BOOK_UPDATE_ID + 4],
        data[BOOK_UPDATE_ID + 5],
        data[BOOK_UPDATE_ID + 6],
        data[BOOK_UPDATE_ID + 7],
    ]) as u64;

    let price_exponent = data[PRICE_EXPONENT] as i8;
    let qty_exponent = data[QTY_EXPONENT] as i8;

    let bid_price_mantissa = i64::from_le_bytes([
        data[BID_PRICE],
        data[BID_PRICE + 1],
        data[BID_PRICE + 2],
        data[BID_PRICE + 3],
        data[BID_PRICE + 4],
        data[BID_PRICE + 5],
        data[BID_PRICE + 6],
        data[BID_PRICE + 7],
    ]);

    let bid_qty_mantissa = i64::from_le_bytes([
        data[BID_QTY],
        data[BID_QTY + 1],
        data[BID_QTY + 2],
        data[BID_QTY + 3],
        data[BID_QTY + 4],
        data[BID_QTY + 5],
        data[BID_QTY + 6],
        data[BID_QTY + 7],
    ]);

    let ask_price_mantissa = i64::from_le_bytes([
        data[ASK_PRICE],
        data[ASK_PRICE + 1],
        data[ASK_PRICE + 2],
        data[ASK_PRICE + 3],
        data[ASK_PRICE + 4],
        data[ASK_PRICE + 5],
        data[ASK_PRICE + 6],
        data[ASK_PRICE + 7],
    ]);

    let ask_qty_mantissa = i64::from_le_bytes([
        data[ASK_QTY],
        data[ASK_QTY + 1],
        data[ASK_QTY + 2],
        data[ASK_QTY + 3],
        data[ASK_QTY + 4],
        data[ASK_QTY + 5],
        data[ASK_QTY + 6],
        data[ASK_QTY + 7],
    ]);

    // 转换mantissa + exponent为f64
    let bid_price = mantissa_exponent_to_f64(bid_price_mantissa, price_exponent);
    let bid_qty = mantissa_exponent_to_f64(bid_qty_mantissa, qty_exponent);
    let ask_price = mantissa_exponent_to_f64(ask_price_mantissa, price_exponent);
    let ask_qty = mantissa_exponent_to_f64(ask_qty_mantissa, qty_exponent);

    // 解析变长symbol字段
    // 检查是否有足够的数据包含symbol字段
    if data.len() <= SYMBOL_LENGTH_OFFSET {
        // 如果没有symbol字段，使用默认值
        let symbol = "UNKNOWN";

        // 验证数据有效性
        if bid_price <= 0.0 || ask_price <= 0.0 {
            return None;
        }

        return Some(SbeBookTicker {
            symbol,
            bid_price,
            bid_qty,
            ask_price,
            ask_qty,
            event_time,
            book_update_id,
        });
    }

    let symbol_length = data[SYMBOL_LENGTH_OFFSET] as usize;

    if data.len() < SYMBOL_DATA_OFFSET + symbol_length {
        return None;
    }

    let symbol_bytes = &data[SYMBOL_DATA_OFFSET..SYMBOL_DATA_OFFSET + symbol_length];
    let symbol = match std::str::from_utf8(symbol_bytes) {
        Ok(s) => s,
        Err(_) => {
            return None;
        }
    };

    // 验证数据有效性
    if symbol.is_empty() || bid_price <= 0.0 || ask_price <= 0.0 {
        return None;
    }

    Some(SbeBookTicker {
        symbol,
        bid_price,
        bid_qty,
        ask_price,
        ask_qty,
        event_time,
        book_update_id,
    })
}

/// 将SBE BookTicker转换为标准BookTicker格式
impl<'a> From<SbeBookTicker<'a>> for crate::encoding::book_ticker::BookTicker<'a> {
    fn from(sbe_ticker: SbeBookTicker<'a>) -> Self {
        crate::encoding::book_ticker::BookTicker {
            symbol: sbe_ticker.symbol,
            bid_price: sbe_ticker.bid_price,
            bid_qty: sbe_ticker.bid_qty,
            ask_price: sbe_ticker.ask_price,
            ask_qty: sbe_ticker.ask_qty,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_sbe_bookticker_data() -> Vec<u8> {
        use binance_offsets::*;

        let symbol = "BTCUSDT";
        let symbol_bytes = symbol.as_bytes();
        let total_length = SYMBOL_DATA_OFFSET + symbol_bytes.len();
        let mut data = vec![0u8; total_length];

        // 固定字段
        // Event time: 1640995200000000 (microseconds)
        let event_time = 1640995200000000i64;
        data[EVENT_TIME..EVENT_TIME + 8].copy_from_slice(&event_time.to_le_bytes());

        // Book update ID: 12345
        let book_update_id = 12345i64;
        data[BOOK_UPDATE_ID..BOOK_UPDATE_ID + 8].copy_from_slice(&book_update_id.to_le_bytes());

        // Price exponent: -4 (表示价格精度为0.0001)
        data[PRICE_EXPONENT] = (-4i8) as u8;

        // Quantity exponent: -8 (表示数量精度为0.00000001)
        data[QTY_EXPONENT] = (-8i8) as u8;

        // Bid price: 50000.0 -> mantissa = 500000000 (50000.0 * 10^4)
        let bid_price_mantissa = 500000000i64;
        data[BID_PRICE..BID_PRICE + 8].copy_from_slice(&bid_price_mantissa.to_le_bytes());

        // Bid qty: 1.5 -> mantissa = 150000000 (1.5 * 10^8)
        let bid_qty_mantissa = 150000000i64;
        data[BID_QTY..BID_QTY + 8].copy_from_slice(&bid_qty_mantissa.to_le_bytes());

        // Ask price: 50001.0 -> mantissa = 500010000 (50001.0 * 10^4)
        let ask_price_mantissa = 500010000i64;
        data[ASK_PRICE..ASK_PRICE + 8].copy_from_slice(&ask_price_mantissa.to_le_bytes());

        // Ask qty: 2.0 -> mantissa = 200000000 (2.0 * 10^8)
        let ask_qty_mantissa = 200000000i64;
        data[ASK_QTY..ASK_QTY + 8].copy_from_slice(&ask_qty_mantissa.to_le_bytes());

        // 变长字段
        // Symbol length
        data[SYMBOL_LENGTH_OFFSET] = symbol_bytes.len() as u8;

        // Symbol data
        data[SYMBOL_DATA_OFFSET..SYMBOL_DATA_OFFSET + symbol_bytes.len()]
            .copy_from_slice(symbol_bytes);

        data
    }

    #[test]
    fn test_parse_sbe_bookticker() {
        let data = create_test_sbe_bookticker_data();
        let ticker = parse_sbe_bookticker(&data).unwrap();

        assert_eq!(ticker.symbol, "BTCUSDT");
        assert_eq!(ticker.bid_price, 50000.0);
        assert_eq!(ticker.bid_qty, 1.5);
        assert_eq!(ticker.ask_price, 50001.0);
        assert_eq!(ticker.ask_qty, 2.0);
        assert_eq!(ticker.event_time, 1640995200000000);
        assert_eq!(ticker.book_update_id, 12345);
    }

    #[test]
    fn test_parse_sbe_bookticker_invalid_data() {
        // 数据长度不足
        let short_data = vec![0u8; 10];
        assert!(parse_sbe_bookticker(&short_data).is_none());

        // 空symbol（长度为0）
        let mut invalid_data = create_test_sbe_bookticker_data();
        invalid_data[binance_offsets::SYMBOL_LENGTH_OFFSET] = 0; // symbol长度为0
        assert!(parse_sbe_bookticker(&invalid_data).is_none());

        // 无效价格（mantissa为0）
        let mut invalid_price_data = create_test_sbe_bookticker_data();
        let zero_mantissa_bytes = 0i64.to_le_bytes();
        invalid_price_data[binance_offsets::BID_PRICE..binance_offsets::BID_PRICE + 8]
            .copy_from_slice(&zero_mantissa_bytes);
        assert!(parse_sbe_bookticker(&invalid_price_data).is_none());
    }

    #[test]
    fn test_sbe_to_standard_bookticker_conversion() {
        let data = create_test_sbe_bookticker_data();
        let sbe_ticker = parse_sbe_bookticker(&data).unwrap();
        let standard_ticker: crate::encoding::book_ticker::BookTicker = sbe_ticker.into();

        assert_eq!(standard_ticker.symbol, "BTCUSDT");
        assert_eq!(standard_ticker.bid_price, 50000.0);
        assert_eq!(standard_ticker.ask_price, 50001.0);
    }

    #[test]
    fn test_parse_sbe_bookticker_batch() {
        let single_data = create_test_sbe_bookticker_data();
        let mut batch_data = Vec::new();

        // 创建3个相同的消息
        for _ in 0..3 {
            batch_data.extend_from_slice(&single_data);
        }

        let tickers = parse_sbe_bookticker_batch(&batch_data);
        assert_eq!(tickers.len(), 3);

        for ticker in &tickers {
            assert_eq!(ticker.symbol, "BTCUSDT");
            assert_eq!(ticker.bid_price, 50000.0);
        }
    }

    #[test]
    fn test_parse_sbe_bookticker_simd_auto() {
        let data = create_test_sbe_bookticker_data();
        let ticker = parse_sbe_bookticker_simd_auto(&data).unwrap();

        assert_eq!(ticker.symbol, "BTCUSDT");
        assert_eq!(ticker.bid_price, 50000.0);
        assert_eq!(ticker.bid_qty, 1.5);
        assert_eq!(ticker.ask_price, 50001.0);
        assert_eq!(ticker.ask_qty, 2.0);
        assert_eq!(ticker.event_time, 1640995200000000);
        assert_eq!(ticker.book_update_id, 12345);
    }

    #[test]
    fn test_parse_sbe_bookticker_batch_auto() {
        let single_data = create_test_sbe_bookticker_data();
        let mut batch_data = Vec::new();

        // 创建5个相同的消息
        for _ in 0..5 {
            batch_data.extend_from_slice(&single_data);
        }

        let tickers = parse_sbe_bookticker_batch_auto(&batch_data);
        assert_eq!(tickers.len(), 5);

        for ticker in &tickers {
            assert_eq!(ticker.symbol, "BTCUSDT");
            assert_eq!(ticker.bid_price, 50000.0);
            assert_eq!(ticker.ask_price, 50001.0);
        }
    }

    #[test]
    fn test_simd_vs_regular_parsing_consistency() {
        let data = create_test_sbe_bookticker_data();

        // 比较常规解析和SIMD解析的结果
        let regular_result = parse_sbe_bookticker(&data).unwrap();
        let simd_result = parse_sbe_bookticker_simd_auto(&data).unwrap();

        assert_eq!(regular_result.symbol, simd_result.symbol);
        assert_eq!(regular_result.bid_price, simd_result.bid_price);
        assert_eq!(regular_result.bid_qty, simd_result.bid_qty);
        assert_eq!(regular_result.ask_price, simd_result.ask_price);
        assert_eq!(regular_result.ask_qty, simd_result.ask_qty);
        assert_eq!(regular_result.event_time, simd_result.event_time);
        assert_eq!(regular_result.book_update_id, simd_result.book_update_id);
    }

    #[test]
    fn test_batch_simd_vs_regular_batch_consistency() {
        let single_data = create_test_sbe_bookticker_data();
        let mut batch_data = Vec::new();

        // 创建10个相同的消息
        for _ in 0..10 {
            batch_data.extend_from_slice(&single_data);
        }

        let regular_results = parse_sbe_bookticker_batch(&batch_data);
        let simd_results = parse_sbe_bookticker_batch_auto(&batch_data);

        assert_eq!(regular_results.len(), simd_results.len());

        for (regular, simd) in regular_results.iter().zip(simd_results.iter()) {
            assert_eq!(regular.symbol, simd.symbol);
            assert_eq!(regular.bid_price, simd.bid_price);
            assert_eq!(regular.ask_price, simd.ask_price);
        }
    }
}
