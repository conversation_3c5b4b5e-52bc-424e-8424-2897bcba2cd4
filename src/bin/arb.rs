use libc::{SCHED_FIFO, sched_param, sched_setscheduler};
use libwebsocket_rs::{
    engine::{arbitrage_runner::run, latency_measurement::measure},
    log, logln,
};
use std::env;

fn print_usage() {
    log!("Usage: arb [OPTIONS]\n");
    log!("Options:\n");
    log!("  --measure, -m    Run latency measurement before starting arbitrage\n");
    log!("  --no-measure     Skip latency measurement and use default configuration\n");
    log!("  --help, -h       Show this help message\n");
    log!("\n");
    log!("If no options are provided, latency measurement will be run by default.\n");
}

fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();

    // 解析命令行参数
    let mut run_measure = true; // 默认运行测量

    for arg in args.iter().skip(1) {
        match arg.as_str() {
            "--measure" | "-m" => {
                run_measure = true;
            }
            "--no-measure" => {
                run_measure = false;
            }
            "--help" | "-h" => {
                print_usage();
                return Ok(());
            }
            _ => {
                logln!("Unknown argument: {}", arg);
                print_usage();
                return Err("Invalid arguments".into());
            }
        }
    }

    unsafe {
        let param = sched_param { sched_priority: 99 };
        let pid = std::process::id() as i32;
        let ret = sched_setscheduler(pid, SCHED_FIFO, &param);
        if ret != 0 {
            logln!(
                "Warning: Failed to set SCHED_FIFO: {}",
                std::io::Error::last_os_error()
            );
            logln!("running as normal priority...");
        }
    }

    let (best_market_data_ip, best_trade_ip, best_order_ip) = if run_measure {
        logln!("running latency measurement...");
        let result = measure();
        if result.0.is_none() || result.1.is_none() || result.2.is_none() {
            logln!("cannot find best ip, use default config");
        }
        // avoid hit the order placing request limit, sleep 10s
        std::thread::sleep(std::time::Duration::from_secs(10));
        result
    } else {
        logln!("skip latency measurement, use default config");
        (None, None, None)
    };

    run(best_market_data_ip, best_trade_ip, best_order_ip)?;

    Ok(())
}
