import pandas as pd
# pd.options.display.datetime_format = "%Y-%m-%d %H:%M:%S.%f"
pd.options.display.precision = 6          # 浮点同时保留 6 位小数
pd.set_option('display.max_rows', None)      # 行数不限
pd.set_option('display.max_columns', None)   # 列数不限

# 宽度相关
pd.set_option('display.width', None)         # 根据终端自动换行
pd.set_option('display.max_colwidth', None)  # 列内容（字符串）长度不限

# partiusdt= pd.read_parquet("../data/data/partiusdt_trades_20250701_121910.parquet")
# partiusdc = pd.read_parquet("../data/data/partiusdc_trades_20250701_121910.parquet")
# bmtusdt = pd.read_parquet("../data/data/bmtusdt_trades_20250701_121910.parquet")
trade = pd.read_parquet("../data/data/data/gunusdc_trades_20250705_015733.parquet")
bbo = pd.read_parquet("../data/data/data/gunusdc_bbo_20250705_015733.parquet")

start = "2025-07-05 01:56:06.000"
end = "2025-07-05 01:56:08.000"
# partiusdt = partiusdt[(partiusdt['timestamp'] > "2025-07-01 20:15:05.000") & (partiusdt['timestamp'] < "2025-07-01 20:15:06.000")]
trade = trade[(trade['timestamp'] > start) & (trade['timestamp'] < end)]
print(trade)
bbo = bbo[(bbo['timestamp'] > start) & (bbo['timestamp'] < end)]
print(bbo)
# bmtusdt = bmtusdt[(bmtusdt['timestamp'] > "2025-07-01 12:15:05.000") & (bmtusdt['timestamp'] < "2025-07-01 12:17:36.000")]
# bmtusdc = bmtusdc[(bmtusdc['timestamp'] > "2025-07-01 12:15:35.000") & (bmtusdc['timestamp'] < "2025-07-01 12:17:36.000")]

# print(bmtusdt
# print(bmtusdc)